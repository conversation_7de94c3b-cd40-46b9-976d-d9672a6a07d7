{"name": "jira_api", "version": "1.0.0", "author": "<PERSON><PERSON>", "license": "MIT", "scripts": {"start": "nodemon --exec ts-node --files src/index.ts", "start:test": "cross-env NODE_ENV='test' DB_DATABASE='jira_test' npm start", "start:production": "pm2 start --name 'jira_api' node -- -r ./tsconfig-paths.js build/index.js", "build": "tsc", "build:lambda": "npm run build && cp package*.json build/", "deploy": "npm run build:lambda && serverless deploy", "deploy:dev": "npm run build:lambda && serverless deploy --stage dev", "deploy:prod": "npm run build:lambda && serverless deploy --stage prod", "offline": "npm run build:lambda && serverless offline", "sam:build": "npm run build:lambda && sam build", "sam:deploy:dev": "npm run sam:build && sam deploy --parameter-overrides Stage=dev", "sam:deploy:prod": "npm run sam:build && sam deploy --parameter-overrides Stage=prod", "sam:local": "npm run sam:build && sam local start-api --port 3000", "pre-commit": "lint-staged", "dynamodb:start": "npx dynamodb-local --port 8000 --inMemory"}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.839.0", "@aws-sdk/lib-dynamodb": "^3.839.0", "@types/aws-lambda": "^8.10.150", "@types/express-session": "^1.18.1", "@types/passport": "^1.0.17", "@types/passport-google-oauth20": "^2.0.16", "aws-lambda": "^1.0.7", "cors": "^2.8.5", "dotenv": "^8.2.0", "express": "^4.17.1", "express-async-handler": "^1.1.4", "express-session": "^1.18.1", "faker": "^4.1.0", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.15", "module-alias": "^2.2.2", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "reflect-metadata": "^0.1.13", "serverless-http": "^3.2.0", "striptags": "^3.1.1", "uuid": "^11.1.0"}, "devDependencies": {"@types/cors": "^2.8.6", "@types/express": "^4.17.2", "@types/faker": "^4.1.7", "@types/jsonapi-serializer": "^3.6.2", "@types/jsonwebtoken": "^8.3.5", "@types/lodash": "^4.14.149", "@types/node": "^12.12.11", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^2.7.0", "@typescript-eslint/parser": "^2.7.0", "cross-env": "^6.0.3", "eslint": "^6.1.0", "eslint-config-airbnb-base": "^14.0.0", "eslint-config-prettier": "^6.7.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-prettier": "^3.1.1", "lint-staged": "^9.4.3", "nodemon": "^2.0.0", "prettier": "^1.19.1", "serverless-offline": "^14.4.0", "ts-node": "^8.5.2", "tsconfig-paths": "^3.9.0", "typescript": "^5.8.3"}, "_moduleDirectories": ["src"], "lint-staged": {"*.ts": ["eslint --fix", "prettier --write", "git add"]}}