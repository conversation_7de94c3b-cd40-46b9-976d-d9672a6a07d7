import { Comment } from '../models';
import { catchErrors } from 'errors';
import { updateEntity, deleteEntity, createEntity } from 'utils/dynamodb';

export const create = catchErrors(async (req, res) => {
  const comment = await createEntity<Comment>('Comment', req.body);
  res.respond({ comment });
});

export const update = catchErrors(async (req, res) => {
  const comment = await updateEntity<Comment>('Comment', req.params.commentId, req.body);
  res.respond({ comment });
});

export const remove = catchErrors(async (req, res) => {
  await deleteEntity('Comment', req.params.commentId);
  res.respond({ success: true });
});
