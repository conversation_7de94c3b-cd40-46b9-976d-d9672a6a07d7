import {
  GetCommand,
  PutCommand,
  DeleteCommand,
  QueryCommand,
} from '@aws-sdk/lib-dynamodb';
import { v4 as uuidv4 } from 'uuid';

import { User, Project, Issue, Comment, UserEntity, Folder, TableConfigs } from '../models';
import { EntityNotFoundError } from 'errors';
import { getDocumentClient, getTableName } from '../database/dynamoClient';

type ModelType = 'User' | 'Project' | 'Issue' | 'Comment' | 'UserEntity' | 'Folder';
type ModelInstance = User | Project | Issue | Comment | UserEntity | Folder;

// Helper function to clean empty string values that are used in GSI keys
const cleanEmptyStringValues = <T extends ModelInstance>(instance: T, modelType: ModelType): T => {
  const cleanedInstance = { ...instance } as any;
  const tableConfig = TableConfigs[modelType];

  // Get all GSI partition and sort keys
  const gsiKeys = new Set<string>();
  if (tableConfig.globalSecondaryIndexes) {
    tableConfig.globalSecondaryIndexes.forEach(gsi => {
      gsiKeys.add(gsi.partitionKey);
      if (gsi.sortKey) {
        gsiKeys.add(gsi.sortKey);
      }
    });
  }

  // Replace empty string values for GSI keys with default values
  gsiKeys.forEach(key => {
    if (cleanedInstance[key] === '') {
      // For path-related fields, use '/' as default
      if (key === 'path' || key === 'pathNames') {
        cleanedInstance[key] = '/';
      } else {
        // For other fields, remove the empty string
        delete cleanedInstance[key];
      }
    }
  });

  return cleanedInstance as T;
};

export const findEntityOrThrow = async <T extends ModelInstance>(
  modelType: ModelType,
  id: string,
): Promise<T> => {
  const docClient = getDocumentClient();
  const tableName = getTableName(TableConfigs[modelType].tableName);

  const result = await docClient.send(
    new GetCommand({
      TableName: tableName,
      Key: { id },
    }),
  );

  if (!result.Item) {
    throw new EntityNotFoundError(modelType);
  }

  return result.Item as T;
};

export const validateAndSaveEntity = async <T extends ModelInstance>(
  modelType: ModelType,
  instance: T,
  isUpdate: boolean = false,
): Promise<T> => {
  // TODO: Fix validation types compatibility between ValidationRules and FieldValidators
  // const validations = ModelValidations[modelType];
  // if (validations) {
  //   const errorFields = generateErrors(instance, validations);
  //   if (Object.keys(errorFields).length > 0) {
  //     throw new BadUserInputError({ fields: errorFields });
  //   }
  // }

  const docClient = getDocumentClient();
  const tableName = getTableName(TableConfigs[modelType].tableName);

  // Generate ID if creating new entity
  if (!isUpdate && !instance.id) {
    instance.id = uuidv4();
  }

  // Clean up empty string values that are used in GSI keys
  // const cleanedInstance = cleanEmptyStringValues(instance, modelType);
  const cleanedInstance = instance;

  await docClient.send(
    new PutCommand({
      TableName: tableName,
      Item: cleanedInstance,
    }),
  );

  return cleanedInstance;
};

export const createEntity = async <T extends ModelInstance>(
  modelType: ModelType,
  input: Omit<T, 'id' | 'createdAt' | 'updatedAt'>,
): Promise<T> => {
  const now = new Date().toISOString();
  const instance = {
    id: uuidv4(),
    ...input,
    createdAt: now,
    updatedAt: now,
  } as T;

  return validateAndSaveEntity(modelType, instance, false);
};

export const updateEntity = async <T extends ModelInstance>(
  modelType: ModelType,
  id: string,
  input: Partial<Omit<T, 'id' | 'createdAt'>>,
): Promise<T> => {
  const existingEntity = await findEntityOrThrow<T>(modelType, id);
  const updatedEntity = {
    ...existingEntity,
    ...input,
    updatedAt: new Date().toISOString(),
  } as T;

  return validateAndSaveEntity(modelType, updatedEntity, true);
};

export const deleteEntity = async (modelType: ModelType, id: string): Promise<void> => {
  const docClient = getDocumentClient();
  const tableName = getTableName(TableConfigs[modelType].tableName);

  await docClient.send(
    new DeleteCommand({
      TableName: tableName,
      Key: { id },
    }),
  );
};

// Query entities by GSI
export const queryEntitiesByIndex = async <T extends ModelInstance>(
  modelType: ModelType,
  indexName: string,
  partitionKeyValue: string | undefined,
  sortKeyValue?: string,
  limit?: number,
): Promise<T[]> => {
  const docClient = getDocumentClient();
  const tableName = getTableName(TableConfigs[modelType].tableName);

  const queryParams: any = {
    TableName: tableName,
    IndexName: indexName,
  };

  // For root items (undefined partition key), use attribute_not_exists
  if (partitionKeyValue === undefined) {
    queryParams.KeyConditionExpression = 'attribute_not_exists(#pk)';
    queryParams.ExpressionAttributeNames = {
      '#pk':
        TableConfigs[modelType].globalSecondaryIndexes?.find(gsi => gsi.indexName === indexName)
          ?.partitionKey || 'id',
    };
  } else {
    queryParams.KeyConditionExpression = '#pk = :pkval';
    queryParams.ExpressionAttributeNames = {
      '#pk':
        TableConfigs[modelType].globalSecondaryIndexes?.find(gsi => gsi.indexName === indexName)
          ?.partitionKey || 'id',
    };
    queryParams.ExpressionAttributeValues = {
      ':pkval': partitionKeyValue,
    };
  }

  if (sortKeyValue) {
    const sortKey = TableConfigs[modelType].globalSecondaryIndexes?.find(
      gsi => gsi.indexName === indexName,
    )?.sortKey;
    if (sortKey) {
      queryParams.KeyConditionExpression += ' AND #sk = :skval';
      queryParams.ExpressionAttributeNames['#sk'] = sortKey;
      queryParams.ExpressionAttributeValues = {
        ...queryParams.ExpressionAttributeValues,
        ':skval': sortKeyValue,
      };
    }
  }

  if (limit) {
    queryParams.Limit = limit;
  }

  const result = await docClient.send(new QueryCommand(queryParams));
  return (result.Items || []) as T[];
};

// Query entities by type and optional conditions
export const queryEntities = async <T extends ModelInstance>(
  modelType: ModelType,
  indexName?: string,
  conditions?: {
    partitionKey: string;
    partitionValue: string | null | undefined;  // Allow null/undefined for root items
    sortKey?: string;
    sortValue?: string;
    operator?: '=' | '<' | '<=' | '>' | '>=';
  },
  limit?: number,
): Promise<T[]> => {
  const docClient = getDocumentClient();
  const tableName = getTableName(TableConfigs[modelType].tableName);

  const queryParams: any = {
    TableName: tableName,
  };

  if (indexName) {
    queryParams.IndexName = indexName;
  }

  if (conditions) {
    let keyConditionExpression = '#pk = :pkval';
    const expressionAttributeNames: { [key: string]: string } = {
      '#pk': conditions.partitionKey,
    };
    const expressionAttributeValues: { [key: string]: any } = {
      ':pkval': conditions.partitionValue ?? null,  // Use null for undefined values
    };

    if (conditions.sortKey && conditions.sortValue) {
      const operator = conditions.operator || '=';
      keyConditionExpression += ` AND #sk ${operator} :skval`;
      expressionAttributeNames['#sk'] = conditions.sortKey;
      expressionAttributeValues[':skval'] = conditions.sortValue;
    }

    queryParams.KeyConditionExpression = keyConditionExpression;
    queryParams.ExpressionAttributeNames = expressionAttributeNames;
    queryParams.ExpressionAttributeValues = expressionAttributeValues;
  }

  if (limit) {
    queryParams.Limit = limit;
  }

  const result = await docClient.send(new QueryCommand(queryParams));
  return (result.Items || []) as T[];
};

// For backward compatibility, keep scanEntities but implement it using queryEntities
export const scanEntities = async <T extends ModelInstance>(
  modelType: ModelType,
  limit?: number,
): Promise<T[]> => {
  const config = TableConfigs[modelType];
  // Use the table's partition key for querying
  return queryEntities(modelType, undefined, {
    partitionKey: config.partitionKey,
    partitionValue: '*', // This will match all items
  }, limit);
};
