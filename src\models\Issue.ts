import { BaseModel, TableConfig, ValidationRules } from './base';
// import is from '../utils/validation';
import { IssueType, IssueStatus, IssuePriority } from '../constants/issues';

export interface Issue extends BaseModel {
  title: string;
  type: IssueType;
  status: IssueStatus;
  priority: IssuePriority;
  listPosition: number;
  description?: string;
  descriptionText?: string;
  estimate?: number;
  timeSpent?: number;
  timeRemaining?: number;
  createdBy: string; // User ID
  reporterId: string; // User ID
  projectId: string; // Project ID
  userIds: string[]; // Array of User IDs assigned to this issue
}

export const IssueValidations: ValidationRules = {
  // TODO: Fix validation types compatibility
  // title: [is.required(), is.maxLength(200)],
  // type: [is.required(), is.oneOf(Object.values(IssueType))],
  // status: [is.required(), is.oneOf(Object.values(IssueStatus))],
  // priority: [is.required(), is.oneOf(Object.values(IssuePriority))],
  // listPosition: is.required(),
  // createdBy: is.required(),
  // reporterId: is.required(),
  // projectId: is.required(),
};

export const IssueTableConfig: TableConfig = {
  tableName: 'Issues',
  partitionKey: 'id',
  globalSecondaryIndexes: [
    {
      indexName: 'ProjectIndex',
      partitionKey: 'projectId',
      sortKey: 'listPosition',
      projectionType: 'ALL',
    },
    {
      indexName: 'CreatedByIndex',
      partitionKey: 'createdBy',
      sortKey: 'createdAt',
      projectionType: 'ALL',
    },
    {
      indexName: 'ReporterIndex',
      partitionKey: 'reporterId',
      sortKey: 'createdAt',
      projectionType: 'ALL',
    },
    {
      indexName: 'StatusIndex',
      partitionKey: 'status',
      sortKey: 'updatedAt',
      projectionType: 'ALL',
    },
  ],
};

// Helper function to create a new Issue
export const createIssue = (issueData: Omit<Issue, 'id' | 'createdAt' | 'updatedAt'>): Issue => {
  const now = new Date().toISOString();
  return {
    id: '', // Will be set by the database layer
    ...issueData,
    userIds: issueData.userIds || [],
    createdAt: now,
    updatedAt: now,
  };
};

// Helper function to update an Issue
export const updateIssue = (
  existingIssue: Issue,
  updates: Partial<Omit<Issue, 'id' | 'createdAt'>>,
): Issue => {
  const updatedIssue = {
    ...existingIssue,
    ...updates,
    updatedAt: new Date().toISOString(),
  };

  // Update descriptionText when description changes
  if (updates.description !== undefined) {
    const striptags = require('striptags');
    updatedIssue.descriptionText = updates.description ? striptags(updates.description) : undefined;
  }

  return updatedIssue;
};
