import { Project, User, UserEntity, Issue } from '../models';
import { catchErrors } from 'errors';
import {
  createEntity,
  findEntityOrThrow,
  updateEntity,
  queryEntitiesByIndex,
  scanEntities,
} from 'utils/dynamodb';
import { issuePartial } from 'serializers/issues';

export const getProjectWithUsersAndIssues = catchErrors(async (_req, res) => {
  const project = await findEntityOrThrow<Project>('Project', '1'); // Convert to string for DynamoDB

  // Get issues for this project
  const issues = await queryEntitiesByIndex<Issue>('Issue', 'ProjectIndex', '1');

  res.respond({
    project: {
      ...project,
      issues: issues.map(issuePartial),
    },
  });
});

export const getProjects = catchErrors(async (_, res) => {
  const projects = await scanEntities<Project>('Project');

  // Get issues for each project
  const projectsWithIssues = await Promise.all(
    projects.map(async project => {
      const issues = await queryEntitiesByIndex<Issue>('Issue', 'ProjectIndex', project.id);
      return { ...project, issues };
    }),
  );

  res.respond({
    projects: projectsWithIssues,
  });
});

export const getProject = catchErrors(async (req, res) => {
  const { projectId } = req.params;
  const project = await findEntityOrThrow<Project>('Project', projectId);

  // Get issues for this project
  const issues = await queryEntitiesByIndex<Issue>('Issue', 'ProjectIndex', projectId);

  // Get user entities for this project
  const userEntities = await queryEntitiesByIndex<UserEntity>(
    'UserEntity',
    'EntityIndex',
    projectId,
  );

  // Get user details for each user entity
  const users = await Promise.all(
    userEntities
      .filter(ue => ue.entityType === 'project')
      .map(async userEntity => {
        try {
          const user = await findEntityOrThrow<User>('User', userEntity.userId);
          return {
            id: user.id,
            name: user.name,
            email: user.email,
            avatarUrl: user.avatarUrl,
            role: user.role,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
            lastLogin: user.lastLogin,
            accessType: userEntity.accessType,
            projectId: userEntity.entityId,
          };
        } catch (error) {
          return null;
        }
      }),
  );

  res.respond({
    ...project,
    issues,
    users: users.filter(user => user !== null),
  });
});

export const update = catchErrors(async (req, res) => {
  const project = await updateEntity<Project>('Project', '1', req.body);
  res.respond({ project });
});

export const create = catchErrors(async (req, res) => {
  const currentUser = req.currentUser as User;
  const userId = currentUser?.id; // Assuming the authenticated user's ID is available in `req.user`

  if (!userId) {
    return res.status(401).respond({ message: 'User not authenticated' });
  }

  const project = await createEntity<Project>('Project', {
    ...req.body,
    createdBy: userId,
  });

  const userProject = await createEntity<UserEntity>('UserEntity', {
    userId,
    entityId: project.id,
    entityType: 'project',
    role: 'lead',
    accessType: ['read', 'write', 'admin'],
  });

  res.respond({ project, userProject });
});
