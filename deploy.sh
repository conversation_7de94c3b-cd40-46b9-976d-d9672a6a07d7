#!/bin/bash
set -e

echo "🚀 Starting deployment..."

# Build application
echo "📦 Building application..."
npm run build

# Docker operations
echo "🐳 Building Docker image..."
docker build -t zetpad-api .

echo "🏷️ Tagging image..."
docker tag zetpad-api:latest 891377014356.dkr.ecr.ap-south-2.amazonaws.com/zetpad-api:latest

echo "🔐 Authenticating with ECR..."
aws ecr get-login-password --region ap-south-2 | docker login --username AWS --password-stdin 891377014356.dkr.ecr.ap-south-2.amazonaws.com

echo "⬆️ Pushing to ECR..."
docker push 891377014356.dkr.ecr.ap-south-2.amazonaws.com/zetpad-api:latest

echo "🔄 Updating Lambda function..."
aws lambda update-function-code \
  --function-name zetpad-api \
  --image-uri 891377014356.dkr.ecr.ap-south-2.amazonaws.com/zetpad-api:latest

echo "⏳ Waiting for update to complete..."
aws lambda wait function-updated --function-name zetpad-api

echo "✅ Testing deployment..."
curl -X GET "https://y15r443i3k.execute-api.ap-south-2.amazonaws.com/prod/health"

echo "🎉 Deployment completed successfully!"
