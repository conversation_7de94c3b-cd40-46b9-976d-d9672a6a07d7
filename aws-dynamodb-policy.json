{"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["dynamodb:CreateTable", "dynamodb:DescribeTable", "dynamodb:PutItem", "dynamodb:GetItem", "dynamodb:UpdateItem", "dynamodb:DeleteItem", "dynamodb:Query", "dynamodb:<PERSON><PERSON>", "dynamodb:BatchGetItem", "dynamodb:BatchWriteItem", "dynamodb:DescribeTimeToLive", "dynamodb:PutTimeToLive", "dynamodb:DescribeStream", "dynamodb:GetRecords", "dynamodb:GetShardIterator", "dynamodb:ListStreams", "dynamodb:ListTables"], "Resource": ["arn:aws:dynamodb:ap-south-2:891377014356:table/zetpad_dev_*", "arn:aws:dynamodb:ap-south-2:891377014356:table/zetpad_dev_*/index/*"]}]}