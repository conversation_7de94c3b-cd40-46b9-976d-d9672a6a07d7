import { Folder, Project } from '../models';
import { Request, Response } from 'express';
import { catchErrors } from 'errors';
import {
  createEntity,
  updateEntity,
  findEntityOrThrow,
  queryEntitiesByIndex,
  deleteEntity,
  queryEntities,
} from 'utils/dynamodb';

// If we want any details of particular Folder, we can use it either for breadcrum or for other details like getProjects, notes, etc.. for that particular folder.
//  Remenber that we are using this api for fetching details only and need to maintain seperate api for details as such for the requirement.
export const getFolderDetails = catchErrors(async (req, res) => {
  const { id } = req.params;
  const folder = await findEntityOrThrow<Folder>('Folder', id);

  res.respond({ folder });
});

export const getSubFolders = catchErrors(async (req, res) => {
  const { parentId } = req.params;

  // Check if the parent folder exists
  try {
    const parentFolder = await findEntityOrThrow<Folder>('Folder', parentId);
  } catch (error) {
    // Explicitly set the HTTP status to 404
    return res.respond({
      message: `Parent folder with ID ${parentId} does not exist`,
      status: 404,
    });
  }

  // Fetch subfolders using GSI
  const folders = await queryEntitiesByIndex<Folder>('Folder', 'ParentIndex', parentId);

  // Fetch projects using GSI
  const projects = await queryEntitiesByIndex<Project>('Project', 'FolderIndex', parentId);

  const combined = [
    ...folders.map(folder => ({ id: folder.id, name: folder.name, type: 'folder' })),
    ...projects.map(project => ({ id: project.id, name: project.name, type: 'project' })),
  ];

  const sortedCombined = combined.sort((a, b) => {
    const nameA = a.name.toLowerCase();
    const nameB = b.name.toLowerCase();

    if (nameA < nameB) return -1; // "a" comes before "b"
    if (nameA > nameB) return 1; // "b" comes before "a"
    return 0; // They are the same
  });

  res.respond({ data: sortedCombined, status: 200 });
});

export const getRootFolders = catchErrors(async (_: Request, res: Response) => {
  // Fetch root folders using PathIndex where path = '/'
  const folders = await queryEntitiesByIndex<Folder>('Folder', 'PathIndex', '/');

  // Fetch root projects using FolderIndex where folderId = '/'
  const projects = await queryEntitiesByIndex<Project>('Project', 'FolderIndex', '/');

  const combined = [
    ...folders.map(folder => ({ id: folder.id, name: folder.name, type: 'folder' })),
    ...projects.map(project => ({ id: project.id, name: project.name, type: 'project' })),
  ];

  const sortedCombined = combined.sort((a, b) => {
    const nameA = a.name.toLowerCase();
    const nameB = b.name.toLowerCase();

    if (nameA < nameB) return -1; // "a" comes before "b"
    if (nameA > nameB) return 1; // "b" comes before "a"
    return 0; // They are the same
  });

  res.respond({ data: sortedCombined, status: 200 });
});

export const createFolder = catchErrors(async (req, res) => {
  const { name, createdBy, parent } = req.body;

  // Validate request body
  if (!name || !createdBy) {
    return res.status(400).respond({ message: 'Name and createdBy are required' });
  }

  let parentFolder = null;

  // Validate parent folder existence if not root
  if (parent && parent !== '/') {
    try {
      parentFolder = await findEntityOrThrow<Folder>('Folder', parent);
    } catch (error) {
      return res.status(404).respond({ message: `Parent folder with ID ${parent} does not exist` });
    }
  }

  // For root folders, use '/' as parent and path
  const path = parentFolder ? `${parentFolder.path}/${parentFolder.id}` : '/';
  const pathNames = parentFolder ? `${parentFolder.pathNames}/${parentFolder.name}` : '/';

  // Create the new folder
  const folder = await createEntity<Folder>('Folder', {
    name,
    createdBy,
    parent: parentFolder ? parentFolder.id : '/',  // Use '/' for root folders
    path,
    pathNames,
  });

  res.respond({ folder });
});

export const updateFolder = catchErrors(async (req, res) => {
  const { id } = req.params;
  const { name } = req.body;

  const folder = await findEntityOrThrow<Folder>('Folder', id);

  const updatedFolder = await updateEntity<Folder>('Folder', id, req.body);

  res.respond({ folder: updatedFolder });
});

export const deleteFolder = catchErrors(async (req: Request, res: Response) => {
  const { id } = req.params;

  if (!id) {
    return res.status(400).respond({ message: 'Folder ID is required' });
  }

  // Fetch the folder to ensure it exists
  try {
    const folder = await findEntityOrThrow<Folder>('Folder', id);
  } catch (error) {
    return res.status(404).respond({ message: 'Folder not found' });
  }

  // Recursive function to delete child folders and their projects
  const deleteFolderRecursively = async (folderId: string): Promise<void> => {
    // Find child folders using GSI
    const childFolders = await queryEntitiesByIndex<Folder>('Folder', 'ParentIndex', folderId);

    // Recursively delete child folders
    for (const child of childFolders) {
      await deleteFolderRecursively(child.id);
    }

    // Delete associated projects using GSI
    const associatedProjects = await queryEntitiesByIndex<Project>(
      'Project',
      'FolderIndex',
      folderId,
    );
    for (const project of associatedProjects) {
      await deleteEntity('Project', project.id);
    }

    // Delete the folder
    await deleteEntity('Folder', folderId);
  };

  // Start recursive deletion from the target folder
  await deleteFolderRecursively(id);

  res.respond({
    message: 'Folder and all associated child folders and projects deleted successfully',
  });
});
