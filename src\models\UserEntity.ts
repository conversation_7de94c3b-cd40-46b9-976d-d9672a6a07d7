import { BaseModel, TableConfig, ValidationRules } from './base';
// import is from '../utils/validation';

export type EntityType = 'folder' | 'room' | 'project' | 'note' | 'task' | 'doc' | 'board';

export interface UserEntity extends BaseModel {
  userId: string; // User ID
  entityId: string; // Foreign key to either Project, Note, Task, etc.
  entityType: EntityType; // Identifies the entity type
  accessType: string[]; // Array of access permissions
  role: string;
}

export const UserEntityValidations: ValidationRules = {
  // TODO: Fix validation types compatibility
  // userId: is.required(),
  // entityId: is.required(),
  // entityType: [is.required(), is.oneOf(['folder', 'room', 'project', 'note', 'task', 'doc', 'board'])],
  // role: [is.required(), is.maxLength(100)],
};

export const UserEntityTableConfig: TableConfig = {
  tableName: 'UserEntities',
  partitionKey: 'id',
  globalSecondaryIndexes: [
    {
      indexName: 'UserIndex',
      partitionKey: 'userId',
      sortKey: 'entityType',
      projectionType: 'ALL',
    },
    {
      indexName: 'EntityIndex',
      partitionKey: 'entityId',
      sortKey: 'userId',
      projectionType: 'ALL',
    },
    {
      indexName: 'EntityTypeIndex',
      partitionKey: 'entityType',
      sortKey: 'createdAt',
      projectionType: 'ALL',
    },
  ],
};

// Helper function to create a new UserEntity
export const createUserEntity = (
  userEntityData: Omit<UserEntity, 'id' | 'createdAt' | 'updatedAt'>,
): UserEntity => {
  const now = new Date().toISOString();
  return {
    id: '', // Will be set by the database layer
    ...userEntityData,
    accessType: userEntityData.accessType || [],
    createdAt: now,
    updatedAt: now,
  };
};

// Helper function to update a UserEntity
export const updateUserEntity = (
  existingUserEntity: UserEntity,
  updates: Partial<Omit<UserEntity, 'id' | 'createdAt'>>,
): UserEntity => {
  return {
    ...existingUserEntity,
    ...updates,
    updatedAt: new Date().toISOString(),
  };
};
