import { BaseModel, TableConfig, ValidationRules } from './base';
// import is from '../utils/validation';

export interface User extends BaseModel {
  name: string;
  email: string;
  avatarUrl: string;
  googleLogin?: string;
  password?: string;
  role: string;
  lastLogin?: string; // ISO string
}

export const UserValidations: ValidationRules = {
  // TODO: Fix validation types compatibility
  // name: [is.required(), is.maxLength(100)],
  // email: [is.required(), is.email(), is.maxLength(200)],
};

export const UserTableConfig: TableConfig = {
  tableName: 'Users',
  partitionKey: 'id',
  globalSecondaryIndexes: [
    {
      indexName: 'EmailIndex',
      partitionKey: 'email',
      projectionType: 'ALL',
    },
    {
      indexName: 'GoogleLoginIndex',
      partitionKey: 'googleLogin',
      projectionType: 'ALL',
    },
  ],
};

// Helper function to create a new User
export const createUser = (userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): User => {
  const now = new Date().toISOString();
  return {
    id: '', // Will be set by the database layer
    ...userData,
    role: userData.role || 'user',
    createdAt: now,
    updatedAt: now,
  };
};

// Helper function to update a User
export const updateUser = (
  existingUser: User,
  updates: Partial<Omit<User, 'id' | 'createdAt'>>,
): User => {
  return {
    ...existingUser,
    ...updates,
    updatedAt: new Date().toISOString(),
  };
};
