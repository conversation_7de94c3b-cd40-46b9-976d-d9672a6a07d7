import passport from 'passport';
import { Strategy as GoogleStrategy } from 'passport-google-oauth20';
import { User } from '../models'; // Assuming User model exists in your application
import { queryEntities, createEntity, findEntityOrThrow } from './dynamodb';

// Define a type for the Google profile
interface GoogleProfile {
  id: string;
  displayName: string;
  emails?: { value: string }[];
  photos?: { value: string }[]; // Add photos to the interface
}

// Configure the Google Strategy
passport.use(
  new GoogleStrategy(
    {
      clientID: process.env.GOOGLE_CLIENT_ID || '',
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
      callbackURL: `${process.env.BACKEND_URL || 'https://y15r443i3k.execute-api.ap-south-2.amazonaws.com/prod'}/auth/google/callback`,
      scope: ['profile', 'email'],
    },
    async (_accessToken, _refreshToken, profile: GoogleProfile, done) => {
      try {
        // Look for existing user by Google login using the GSI
        const existingUsers = await queryEntities<User>(
          'User',
          'GoogleLoginIndex',
          {
            partitionKey: 'googleLogin',
            partitionValue: profile.id,
          }
        );
        const existingUser = existingUsers.length > 0 ? existingUsers[0] : null;

        if (!existingUser) {
          const email =
            profile.emails && profile.emails.length > 0 ? profile.emails[0].value : null;
          const avatarUrl =
            profile.photos && profile.photos.length > 0 ? profile.photos[0].value : null;

          if (!email) {
            return done(new Error('No email found for Google account.'));
          }

          const newUser = await createEntity<User>('User', {
            googleLogin: profile.id,
            email,
            name: profile.displayName,
            avatarUrl: avatarUrl || '',
            role: 'user',
          });

          return done(null, newUser);
        }

        return done(null, existingUser);
      } catch (error) {
        console.error('Error during Google authentication:', error);
        return done(error);
      }
    },
  ),
);

// Serialize user to the session
passport.serializeUser((user: any, done) => {
  done(null, user.id);
});

// Deserialize user from the session
passport.deserializeUser(async (id: string, done) => {
  try {
    const user = await findEntityOrThrow<User>('User', id);
    done(null, user);
  } catch (error) {
    console.error('Error deserializing user:', error);
    done(error);
  }
});
