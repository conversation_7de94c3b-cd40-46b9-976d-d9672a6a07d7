# AWS Lambda Deployment Guide for Zetpad API

## Overview

Your Zetpad API has been successfully configured for AWS Lambda deployment. This guide provides multiple deployment options.

## 🎉 What's Been Set Up

✅ **Lambda Handler**: `src/lambda.ts` - Serverless-compatible Express app  
✅ **Serverless Config**: `serverless.yml` - Deployment configuration  
✅ **Build System**: TypeScript compilation with Lambda-specific scripts  
✅ **Environment Config**: `.env.lambda` - Lambda environment variables  
✅ **DynamoDB Integration**: Already configured for AWS DynamoDB  

## 📁 Project Structure

```
├── src/
│   ├── lambda.ts          # Lambda handler (NEW)
│   ├── index.ts           # Original Express server
│   └── ...                # Your existing code
├── build/                 # Compiled JavaScript (auto-generated)
├── serverless.yml         # Serverless Framework config (NEW)
├── .env.lambda           # Lambda environment variables (NEW)
└── package.json          # Updated with Lambda scripts
```

## 🚀 Deployment Options

### Option 1: Serverless Framework (Recommended)

#### Prerequisites
```bash
# Install Serverless CLI globally (already done)
npm install -g serverless

# Login to Serverless (free for small projects)
serverless login
```

#### Deploy Commands
```bash
# Deploy to development
npm run deploy:dev

# Deploy to production  
npm run deploy:prod

# Custom deployment
npm run build:lambda && serverless deploy --stage your-stage --region your-region
```

#### Environment Variables
Before deploying, update these in your AWS Lambda console or serverless.yml:
- `JWT_SECRET`: Your JWT secret key
- `GOOGLE_CLIENT_ID`: Your Google OAuth client ID
- `GOOGLE_CLIENT_SECRET`: Your Google OAuth client secret
- `FRONTEND_URL`: Your frontend application URL
- `CORS_ORIGIN`: Allowed CORS origins

### Option 2: AWS CLI + ZIP Deployment

#### Prerequisites
```bash
# Install AWS CLI
# https://aws.amazon.com/cli/

# Configure AWS credentials
aws configure
```

#### Manual Deployment Steps
```bash
# 1. Build the project
npm run build:lambda

# 2. Create deployment package
cd build
zip -r ../zetpad-api-lambda.zip . -x "*.map"
cd ..

# 3. Create Lambda function (first time only)
aws lambda create-function \
  --function-name zetpad-api \
  --runtime nodejs18.x \
  --role arn:aws:iam::YOUR_ACCOUNT:role/lambda-execution-role \
  --handler lambda.handler \
  --zip-file fileb://zetpad-api-lambda.zip \
  --timeout 30 \
  --memory-size 512

# 4. Update function code (subsequent deployments)
aws lambda update-function-code \
  --function-name zetpad-api \
  --zip-file fileb://zetpad-api-lambda.zip
```

### Option 3: AWS Console Upload

1. **Build the project**: `npm run build:lambda`
2. **Create ZIP file**: Compress the `build/` directory contents
3. **Go to AWS Lambda Console**: https://console.aws.amazon.com/lambda/
4. **Create Function**:
   - Function name: `zetpad-api`
   - Runtime: `Node.js 18.x`
   - Handler: `lambda.handler`
5. **Upload ZIP file** in the Code section
6. **Configure Environment Variables** (see list above)
7. **Set up API Gateway** to trigger the Lambda function

## 🔧 Configuration

### Required IAM Permissions
Your Lambda execution role needs these DynamoDB permissions:
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "dynamodb:Query",
        "dynamodb:Scan", 
        "dynamodb:GetItem",
        "dynamodb:PutItem",
        "dynamodb:UpdateItem",
        "dynamodb:DeleteItem"
      ],
      "Resource": [
        "arn:aws:dynamodb:*:*:table/zetpad_*",
        "arn:aws:dynamodb:*:*:table/zetpad_*/index/*"
      ]
    }
  ]
}
```

### Environment Variables for Lambda
Set these in your Lambda function configuration:
```
NODE_ENV=production
AWS_REGION=ap-south-2
TABLE_PREFIX=zetpad_prod_
JWT_SECRET=your-jwt-secret-here
SESSION_SECRET=your-session-secret-here
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
FRONTEND_URL=https://your-frontend-domain.com
CORS_ORIGIN=https://your-frontend-domain.com
```

## 🧪 Testing

### Local Testing (Serverless Offline)
```bash
# Test locally (requires Serverless login)
npm run offline

# Your API will be available at:
# http://localhost:3000
```

### Health Check
After deployment, test your Lambda function:
```bash
curl https://your-api-gateway-url/health
```

Expected response:
```json
{
  "status": "OK",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## 📝 Important Notes

### Sessions vs JWT
- **Sessions don't work well with Lambda** due to stateless nature
- Consider using **JWT tokens** for authentication instead
- Current implementation includes session middleware but may not persist

### Cold Starts
- First request after inactivity may be slower (cold start)
- Consider using **Provisioned Concurrency** for production

### Database Connections
- DynamoDB connections are handled automatically
- No connection pooling needed (unlike traditional databases)

### CORS Configuration
- Update `CORS_ORIGIN` environment variable
- Configure API Gateway CORS if needed

## 🔄 CI/CD Pipeline

### GitHub Actions Example
Create `.github/workflows/deploy.yml`:
```yaml
name: Deploy to AWS Lambda
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      - run: npm install
      - run: npm run build:lambda
      - uses: serverless/github-action@v3
        with:
          args: deploy --stage prod
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
```

## 🆘 Troubleshooting

### Common Issues
1. **Module not found**: Ensure all dependencies are in `package.json`
2. **Timeout errors**: Increase Lambda timeout (max 15 minutes)
3. **Memory errors**: Increase Lambda memory allocation
4. **DynamoDB permissions**: Check IAM role permissions
5. **CORS errors**: Configure CORS_ORIGIN environment variable

### Logs
View Lambda logs in CloudWatch:
```bash
# Using AWS CLI
aws logs tail /aws/lambda/zetpad-api --follow

# Or use Serverless
serverless logs -f api --tail
```

## 🎯 Next Steps

1. **Deploy to development** environment first
2. **Test all API endpoints** thoroughly  
3. **Set up monitoring** with CloudWatch
4. **Configure custom domain** with API Gateway
5. **Set up CI/CD pipeline** for automated deployments

Your Zetpad API is now ready for serverless deployment! 🚀
