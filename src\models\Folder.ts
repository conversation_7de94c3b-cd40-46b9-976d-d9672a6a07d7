import { BaseModel, TableConfig, ValidationRules } from './base';
// import is from '../utils/validation';

export interface Folder extends BaseModel {
  name: string;
  createdBy: string; // User ID
  lastAccessed?: string; // ISO string
  path: string; // Materialized path (e.g., '/1/2/3')
  pathNames?: string; // Path with names (e.g., '/root/folder1/subfolder')
  parent?: string; // Parent folder ID
}

export const FolderValidations: ValidationRules = {
  // TODO: Fix validation types compatibility
  // name: [is.required(), is.maxLength(200)],
  // createdBy: is.required(),
  // path: is.required(),
};

export const FolderTableConfig: TableConfig = {
  tableName: 'Folders',
  partitionKey: 'id',
  globalSecondaryIndexes: [
    {
      indexName: 'CreatedByIndex',
      partitionKey: 'createdBy',
      sortKey: 'createdAt',
      projectionType: 'ALL',
    },
    {
      indexName: 'ParentIndex',
      partitionKey: 'parent',
      sortKey: 'name',
      projectionType: 'ALL',
    },
    {
      indexName: 'PathIndex',
      partitionKey: 'path',
      projectionType: 'ALL',
    },
  ],
};

// Helper function to create a new Folder
export const createFolder = (
  folderData: Omit<Folder, 'id' | 'createdAt' | 'updatedAt'>,
): Folder => {
  const now = new Date().toISOString();
  return {
    id: '', // Will be set by the database layer
    ...folderData,
    createdAt: now,
    updatedAt: now,
  };
};

// Helper function to update a Folder
export const updateFolder = (
  existingFolder: Folder,
  updates: Partial<Omit<Folder, 'id' | 'createdAt'>>,
): Folder => {
  return {
    ...existingFolder,
    ...updates,
    updatedAt: new Date().toISOString(),
  };
};

// Helper function to build path names from path IDs
export const buildPathNames = async (
  path: string,
  getFolderById: (id: string) => Promise<Folder | null>,
): Promise<string> => {
  if (!path) return '';

  const pathIds = path.split('/').filter(id => id);
  const folderNames = await Promise.all(
    pathIds.map(async id => {
      const folder = await getFolderById(id);
      return folder ? folder.name : `Folder-${id}`;
    }),
  );

  return `/${folderNames.join('/')}`;
};
