import {
  CreateTableCommand,
  DescribeTableCommand,
  ResourceNotFoundException,
  AttributeDefinition,
  KeySchemaElement,
  GlobalSecondaryIndex,
  LocalSecondaryIndex,
} from '@aws-sdk/client-dynamodb';
import { getDynamoDBClient, getTableName } from './dynamoClient';
import { TableConfig, GSIConfig, LSIConfig } from '../models/base';
import { TableConfigs } from '../models';

const createAttributeDefinitions = (config: TableConfig): AttributeDefinition[] => {
  const attributes = new Set<string>();

  // Add partition key
  attributes.add(config.partitionKey);

  // Add sort key if exists
  if (config.sortKey) {
    attributes.add(config.sortKey);
  }

  // Add GSI keys
  config.globalSecondaryIndexes?.forEach(gsi => {
    attributes.add(gsi.partitionKey);
    if (gsi.sortKey) {
      attributes.add(gsi.sortKey);
    }
  });

  // Add LSI keys
  config.localSecondaryIndexes?.forEach(lsi => {
    attributes.add(lsi.sortKey);
  });

  return Array.from(attributes).map(attr => ({
    AttributeName: attr,
    AttributeType: 'S', // All our keys are strings
  }));
};

const createKeySchema = (partitionKey: string, sortKey?: string): KeySchemaElement[] => {
  const keySchema: KeySchemaElement[] = [
    {
      AttributeName: partitionKey,
      KeyType: 'HASH',
    },
  ];

  if (sortKey) {
    keySchema.push({
      AttributeName: sortKey,
      KeyType: 'RANGE',
    });
  }

  return keySchema;
};

const createGlobalSecondaryIndexes = (gsiConfigs?: GSIConfig[]): GlobalSecondaryIndex[] => {
  if (!gsiConfigs) return [];

  return gsiConfigs.map(gsi => ({
    IndexName: gsi.indexName,
    KeySchema: createKeySchema(gsi.partitionKey, gsi.sortKey),
    Projection: {
      ProjectionType: gsi.projectionType || 'ALL',
      NonKeyAttributes: gsi.projectedAttributes,
    },
    ProvisionedThroughput: {
      ReadCapacityUnits: 5,
      WriteCapacityUnits: 5,
    },
  }));
};

const createLocalSecondaryIndexes = (lsiConfigs?: LSIConfig[]): LocalSecondaryIndex[] => {
  if (!lsiConfigs) return [];

  return lsiConfigs.map(lsi => ({
    IndexName: lsi.indexName,
    KeySchema: [
      {
        AttributeName: 'id', // LSI uses the same partition key as the table
        KeyType: 'HASH',
      },
      {
        AttributeName: lsi.sortKey,
        KeyType: 'RANGE',
      },
    ],
    Projection: {
      ProjectionType: lsi.projectionType || 'ALL',
      NonKeyAttributes: lsi.projectedAttributes,
    },
  }));
};

export const createTable = async (config: TableConfig): Promise<void> => {
  const client = getDynamoDBClient();
  const tableName = getTableName(config.tableName);

  try {
    // Check if table already exists
    await client.send(new DescribeTableCommand({ TableName: tableName }));
    console.log(`Table ${tableName} already exists`);
    return;
  } catch (error) {
    if (!(error instanceof ResourceNotFoundException)) {
      throw error;
    }
  }

  const createTableParams = {
    TableName: tableName,
    AttributeDefinitions: createAttributeDefinitions(config),
    KeySchema: createKeySchema(config.partitionKey, config.sortKey),
    GlobalSecondaryIndexes: createGlobalSecondaryIndexes(config.globalSecondaryIndexes),
    LocalSecondaryIndexes: createLocalSecondaryIndexes(config.localSecondaryIndexes),
    ProvisionedThroughput: {
      ReadCapacityUnits: 5,
      WriteCapacityUnits: 5,
    },
  };

  // Remove empty arrays
  if (createTableParams.GlobalSecondaryIndexes?.length === 0) {
    delete (createTableParams as any).GlobalSecondaryIndexes;
  }
  if (createTableParams.LocalSecondaryIndexes?.length === 0) {
    delete (createTableParams as any).LocalSecondaryIndexes;
  }

  try {
    await client.send(new CreateTableCommand(createTableParams));
    console.log(`Table ${tableName} created successfully`);
  } catch (error) {
    console.error(`Error creating table ${tableName}:`, error);
    throw error;
  }
};

export const createAllTables = async (): Promise<void> => {
  console.log('Creating DynamoDB tables...');

  for (const [modelName, config] of Object.entries(TableConfigs)) {
    try {
      await createTable(config);
    } catch (error) {
      console.error(`Failed to create table for ${modelName}:`, error);
      throw error;
    }
  }

  console.log('All tables created successfully');
};
