import 'module-alias/register';
import 'dotenv/config';
import express from 'express';
import cors from 'cors';
import serverless from 'serverless-http';
import { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from 'aws-lambda';
import { signToken } from 'utils/authToken';
import { createDynamoDBConnection } from 'database/dynamoClient';
import { createAllTables } from 'database/createTables';
import { addRespondToResponse } from 'middleware/response';
import { authenticateUser } from 'middleware/authentication';
import { handleError } from 'middleware/errors';
import { RouteNotFoundError } from 'errors';
import passport from 'passport';
import session from 'express-session';
import { User } from './models';
import './utils/passportConfig';
import { attachPublicRoutes, attachPrivateRoutes } from './routes';

let app: express.Application;
let isInitialized = false;

const establishDatabaseConnection = async (): Promise<void> => {
  try {
    // Initialize DynamoDB connection
    createDynamoDBConnection();
    console.log('DynamoDB connection established');

    // Create tables if they don't exist (for development)
    if (process.env.NODE_ENV !== 'production') {
      await createAllTables();
      console.log('DynamoDB tables initialized');
    }
  } catch (error) {
    console.log('Database connection error:', error);
  }
};

const createExpressApp = (): express.Application => {
  const expressApp = express();

  // Note: Sessions don't work well with Lambda due to stateless nature
  // Consider using JWT tokens for authentication instead
  expressApp.use(
    session({
      secret: process.env.SESSION_SECRET || 'default_secret',
      resave: false,
      saveUninitialized: false,
    }),
  );

  // Initialize Passport
  expressApp.use(passport.initialize());
  expressApp.use(passport.session());

  expressApp.use(
    cors({
      origin: process.env.CORS_ORIGIN || '*',
      credentials: true,
    }),
  );

  expressApp.use(express.json());
  expressApp.use(express.urlencoded({ extended: true }));

  expressApp.use(addRespondToResponse);

  // Health check endpoint
  expressApp.get('/health', (req, res) => {
    res.respond({ status: 'OK', timestamp: new Date().toISOString() });
  });

  expressApp.get('/auth/google', passport.authenticate('google', { scope: ['profile', 'email'] }));

  expressApp.get(
    '/auth/google/callback',
    passport.authenticate('google', {
      failureRedirect: process.env.FRONTEND_URL || 'http://localhost:8080/',
      session: true,
    }),
    (req, res) => {
      const user = req.user as User;
      if (user) {
        const generatedToken = signToken({ sub: user.id });
        res.redirect(
          `${process.env.FRONTEND_URL || 'http://localhost:8080'}/login?token=${generatedToken}`,
        );
      } else {
        res.redirect(process.env.FRONTEND_URL || 'http://localhost:8080/');
      }
    },
  );

  attachPublicRoutes(expressApp);

  expressApp.use('/', authenticateUser);

  attachPrivateRoutes(expressApp);

  expressApp.use((req, _res, next) => next(new RouteNotFoundError(req.originalUrl)));
  expressApp.use(handleError);

  return expressApp;
};

const initializeApp = async (): Promise<express.Application> => {
  if (!isInitialized) {
    await establishDatabaseConnection();
    app = createExpressApp();
    isInitialized = true;
  }
  return app;
};

// Lambda handler
export const handler = async (
  event: APIGatewayProxyEvent,
  context: Context,
): Promise<APIGatewayProxyResult> => {
  // Initialize app if not already done
  const expressApp = await initializeApp();

  // Create serverless handler
  const serverlessHandler = serverless(expressApp, {
    binary: ['image/*', 'application/pdf', 'application/octet-stream'],
  });

  return serverlessHandler(event, context) as Promise<APIGatewayProxyResult>;
};
