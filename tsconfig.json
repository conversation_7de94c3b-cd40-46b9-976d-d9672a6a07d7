{"compilerOptions": {"target": "es2020", "module": "commonjs", "lib": ["es2020", "dom"], "sourceMap": true, "outDir": "./build", "removeComments": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": false, "noImplicitThis": true, "alwaysStrict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": true, "moduleResolution": "node", "baseUrl": "src", "paths": {"*": ["./*"]}, "types": ["node"], "skipLibCheck": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "forceConsistentCasingInFileNames": true}, "exclude": ["node_modules"], "include": ["./src/**/*.ts", "./src/types/**/*.d.ts"]}