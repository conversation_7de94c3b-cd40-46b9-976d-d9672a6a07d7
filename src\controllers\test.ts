import { catchErrors } from 'errors';
import { createAllTables } from '../database/createTables';

export const resetDatabase = catchErrors(async (_req, res) => {
  // For DynamoDB, we'll recreate all tables
  // Note: This is a simplified approach for development/testing
  // In production, you'd want more sophisticated table management
  await createAllTables();
  res.respond({ message: 'DynamoDB tables created/reset successfully' });
});
