import { BaseModel, TableConfig, ValidationRules } from './base';
// import is from '../utils/validation';

export interface Comment extends BaseModel {
  body: string;
  userId: string; // User ID
  issueId: string; // Issue ID
}

export const CommentValidations: ValidationRules = {
  // TODO: Fix validation types compatibility
  // body: [is.required(), is.maxLength(50000)],
  // userId: is.required(),
  // issueId: is.required(),
};

export const CommentTableConfig: TableConfig = {
  tableName: 'Comments',
  partitionKey: 'id',
  globalSecondaryIndexes: [
    {
      indexName: 'IssueIndex',
      partitionKey: 'issueId',
      sortKey: 'createdAt',
      projectionType: 'ALL',
    },
    {
      indexName: 'UserIndex',
      partitionKey: 'userId',
      sortKey: 'createdAt',
      projectionType: 'ALL',
    },
  ],
};

// Helper function to create a new Comment
export const createComment = (
  commentData: Omit<Comment, 'id' | 'createdAt' | 'updatedAt'>,
): Comment => {
  const now = new Date().toISOString();
  return {
    id: '', // Will be set by the database layer
    ...commentData,
    createdAt: now,
    updatedAt: now,
  };
};

// Helper function to update a Comment
export const updateComment = (
  existingComment: Comment,
  updates: Partial<Omit<Comment, 'id' | 'createdAt'>>,
): Comment => {
  return {
    ...existingComment,
    ...updates,
    updatedAt: new Date().toISOString(),
  };
};
