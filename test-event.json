{"httpMethod": "GET", "path": "/health", "headers": {"Content-Type": "application/json"}, "multiValueHeaders": {}, "queryStringParameters": null, "multiValueQueryStringParameters": null, "body": null, "isBase64Encoded": false, "requestContext": {"accountId": "************", "apiId": "y15r443i3k", "httpMethod": "GET", "path": "/prod/health", "stage": "prod", "requestId": "test-request-id", "requestTime": "03/Jul/2025:14:47:00 +0000", "requestTimeEpoch": **********, "identity": {"sourceIp": "127.0.0.1"}}, "pathParameters": null, "stageVariables": null}