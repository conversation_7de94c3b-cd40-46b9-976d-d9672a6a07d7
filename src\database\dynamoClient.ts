import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient } from '@aws-sdk/lib-dynamodb';

let dynamoClient: DynamoDBClient;
let docClient: DynamoDBDocumentClient;

export const createDynamoDBConnection = (): {
  client: DynamoDBClient;
  docClient: DynamoDBDocumentClient;
} => {
  if (!dynamoClient) {
    const config: any = {
      region: process.env.AWS_REGION || 'ap-south-2',
    };

    dynamoClient = new DynamoDBClient(config);
    docClient = DynamoDBDocumentClient.from(dynamoClient);
  }

  return { client: dynamoClient, docClient };
};

export const getDynamoDBClient = (): DynamoDBClient => {
  if (!dynamoClient) {
    createDynamoDBConnection();
  }
  return dynamoClient;
};

export const getDocumentClient = (): DynamoDBDocumentClient => {
  if (!docClient) {
    createDynamoDBConnection();
  }
  return docClient;
};

// Helper function to get table name with prefix
export const getTableName = (baseName: string): string => {
  const prefix = process.env.TABLE_PREFIX || '';
  return `${prefix}${baseName}`;
};

export default createDynamoDBConnection;
