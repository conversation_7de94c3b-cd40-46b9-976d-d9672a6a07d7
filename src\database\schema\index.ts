// Import all tables for relations
import { relations } from 'drizzle-orm';
import { users } from './users';
import { folders } from './folders';
import { projects } from './projects';
import { issues } from './issues';
import { issueAssignees } from './issueAssignees';
import { comments } from './comments';
import { userEntities } from './userEntities';

// Export all tables and types
export * from './users';
export * from './folders';
export * from './projects';
export * from './issues';
export * from './issueAssignees';
export * from './comments';
export * from './userEntities';

export const usersRelations = relations(users, ({ many }) => ({
  createdProjects: many(projects, { relationName: 'createdBy' }),
  reportedIssues: many(issues, { relationName: 'reportedBy' }),
  createdIssues: many(issues, { relationName: 'createdBy' }),
  comments: many(comments),
  createdFolders: many(folders),
  issueAssignments: many(issueAssignees),
  userEntities: many(userEntities),
}));

export const foldersRelations = relations(folders, ({ one, many }) => ({
  creator: one(users, {
    fields: [folders.createdBy],
    references: [users.id],
  }),
  parent: one(folders, {
    fields: [folders.parentId],
    references: [folders.id],
    relationName: 'parentChild',
  }),
  children: many(folders, { relationName: 'parentChild' }),
  projects: many(projects),
}));

export const projectsRelations = relations(projects, ({ one, many }) => ({
  creator: one(users, {
    fields: [projects.createdBy],
    references: [users.id],
  }),
  folder: one(folders, {
    fields: [projects.folderId],
    references: [folders.id],
  }),
  issues: many(issues),
}));

export const issuesRelations = relations(issues, ({ one, many }) => ({
  creator: one(users, {
    fields: [issues.createdBy],
    references: [users.id],
    relationName: 'createdBy',
  }),
  reporter: one(users, {
    fields: [issues.reporterId],
    references: [users.id],
    relationName: 'reportedBy',
  }),
  project: one(projects, {
    fields: [issues.projectId],
    references: [projects.id],
  }),
  comments: many(comments),
  assignees: many(issueAssignees),
}));

export const issueAssigneesRelations = relations(issueAssignees, ({ one }) => ({
  issue: one(issues, {
    fields: [issueAssignees.issueId],
    references: [issues.id],
  }),
  user: one(users, {
    fields: [issueAssignees.userId],
    references: [users.id],
  }),
}));

export const commentsRelations = relations(comments, ({ one }) => ({
  user: one(users, {
    fields: [comments.userId],
    references: [users.id],
  }),
  issue: one(issues, {
    fields: [comments.issueId],
    references: [issues.id],
  }),
}));

export const userEntitiesRelations = relations(userEntities, ({ one }) => ({
  user: one(users, {
    fields: [userEntities.userId],
    references: [users.id],
  }),
}));

// Export all tables as a single object for Drizzle
export const schema = {
  users,
  folders,
  projects,
  issues,
  issueAssignees,
  comments,
  userEntities,
  usersRelations,
  foldersRelations,
  projectsRelations,
  issuesRelations,
  issueAssigneesRelations,
  commentsRelations,
  userEntitiesRelations,
};
