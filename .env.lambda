# Lambda Environment Variables
# Copy these to your AWS Lambda environment variables or use them for local testing

NODE_ENV=production
AWS_REGION=ap-south-2
TABLE_PREFIX=zetpad_prod_

# JWT Configuration
JWT_SECRET=your-jwt-secret-here

# Session Configuration (Note: Sessions don't work well with Lambda)
SESSION_SECRET=your-session-secret-here

# Google OAuth Configuration
GOOGLE_CLIENT_ID=535450891467-ika872e7agpd4aismr70pf7je4b1ac35.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# Frontend URL for redirects
FRONTEND_URL=https://your-frontend-domain.com

# CORS Configuration
CORS_ORIGIN=https://your-frontend-domain.com

# DynamoDB Configuration (Leave empty to use AWS DynamoDB service)
# DYNAMODB_ENDPOINT=

# Skip table creation in production (tables should already exist)
SKIP_TABLE_CREATION=true
