export interface BaseModel {
  id: string;
  createdAt: string; // ISO string
  updatedAt: string; // ISO string
}

export interface ValidationRule {
  (value: any): string | false;
}

export interface ValidationRules {
  [key: string]: ValidationRule | ValidationRule[];
}

export interface ModelValidations {
  [key: string]: ValidationRules;
}

// DynamoDB table configuration
export interface TableConfig {
  tableName: string;
  partitionKey: string;
  sortKey?: string;
  globalSecondaryIndexes?: GSIConfig[];
  localSecondaryIndexes?: LSIConfig[];
}

export interface GSIConfig {
  indexName: string;
  partitionKey: string;
  sortKey?: string;
  projectionType?: 'ALL' | 'KEYS_ONLY' | 'INCLUDE';
  projectedAttributes?: string[];
}

export interface LSIConfig {
  indexName: string;
  sortKey: string;
  projectionType?: 'ALL' | 'KEYS_ONLY' | 'INCLUDE';
  projectedAttributes?: string[];
}
