AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: Zetpad API - Serverless Express Application

Parameters:
  Stage:
    Type: String
    Default: dev
    AllowedValues: [dev, staging, prod]
    Description: Deployment stage
  
  JwtSecret:
    Type: String
    NoEcho: true
    Description: JWT Secret for token signing
    
  GoogleClientId:
    Type: String
    Description: Google OAuth Client ID
    
  GoogleClientSecret:
    Type: String
    NoEcho: true
    Description: Google OAuth Client Secret
    
  FrontendUrl:
    Type: String
    Default: http://localhost:8080
    Description: Frontend application URL
    
  CorsOrigin:
    Type: String
    Default: "*"
    Description: CORS allowed origins

Globals:
  Function:
    Timeout: 30
    MemorySize: 512
    Runtime: nodejs18.x
    Environment:
      Variables:
        NODE_ENV: !Ref Stage
        AWS_REGION: !Ref AWS::Region
        TABLE_PREFIX: !Sub "zetpad_${Stage}_"
        JWT_SECRET: !Ref JwtSecret
        SESSION_SECRET: !Ref JwtSecret
        GOOGLE_CLIENT_ID: !Ref GoogleClientId
        GOOGLE_CLIENT_SECRET: !Ref GoogleClientSecret
        FRONTEND_URL: !Ref FrontendUrl
        CORS_ORIGIN: !Ref CorsOrigin

Resources:
  # Lambda Function
  ZetpadApiFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub "zetpad-api-${Stage}"
      CodeUri: build/
      Handler: lambda.handler
      Description: Zetpad API Lambda Function
      
      # API Gateway Event
      Events:
        ApiGateway:
          Type: Api
          Properties:
            Path: /{proxy+}
            Method: ANY
            RestApiId: !Ref ZetpadApiGateway
        ApiGatewayRoot:
          Type: Api
          Properties:
            Path: /
            Method: ANY
            RestApiId: !Ref ZetpadApiGateway
      
      # IAM Role for DynamoDB access
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Sub "zetpad_${Stage}_*"
        - Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Action:
                - dynamodb:Query
                - dynamodb:Scan
                - dynamodb:GetItem
                - dynamodb:PutItem
                - dynamodb:UpdateItem
                - dynamodb:DeleteItem
                - dynamodb:CreateTable
                - dynamodb:DescribeTable
                - dynamodb:ListTables
              Resource:
                - !Sub "arn:aws:dynamodb:${AWS::Region}:${AWS::AccountId}:table/zetpad_${Stage}_*"
                - !Sub "arn:aws:dynamodb:${AWS::Region}:${AWS::AccountId}:table/zetpad_${Stage}_*/index/*"

  # API Gateway
  ZetpadApiGateway:
    Type: AWS::Serverless::Api
    Properties:
      Name: !Sub "zetpad-api-${Stage}"
      StageName: !Ref Stage
      Cors:
        AllowMethods: "'GET,POST,PUT,DELETE,OPTIONS'"
        AllowHeaders: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
        AllowOrigin: !Sub "'${CorsOrigin}'"
        AllowCredentials: true
      
      # Enable API Gateway logging
      AccessLogSetting:
        DestinationArn: !GetAtt ApiGatewayLogGroup.Arn
        Format: >
          {
            "requestId": "$context.requestId",
            "ip": "$context.identity.sourceIp",
            "caller": "$context.identity.caller",
            "user": "$context.identity.user",
            "requestTime": "$context.requestTime",
            "httpMethod": "$context.httpMethod",
            "resourcePath": "$context.resourcePath",
            "status": "$context.status",
            "protocol": "$context.protocol",
            "responseLength": "$context.responseLength"
          }

  # CloudWatch Log Group for API Gateway
  ApiGatewayLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub "/aws/apigateway/zetpad-api-${Stage}"
      RetentionInDays: 14

  # CloudWatch Log Group for Lambda
  LambdaLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub "/aws/lambda/zetpad-api-${Stage}"
      RetentionInDays: 14

Outputs:
  ApiUrl:
    Description: "API Gateway endpoint URL"
    Value: !Sub "https://${ZetpadApiGateway}.execute-api.${AWS::Region}.amazonaws.com/${Stage}/"
    Export:
      Name: !Sub "${AWS::StackName}-ApiUrl"
      
  ApiId:
    Description: "API Gateway ID"
    Value: !Ref ZetpadApiGateway
    Export:
      Name: !Sub "${AWS::StackName}-ApiId"
      
  LambdaFunctionArn:
    Description: "Lambda Function ARN"
    Value: !GetAtt ZetpadApiFunction.Arn
    Export:
      Name: !Sub "${AWS::StackName}-LambdaArn"
