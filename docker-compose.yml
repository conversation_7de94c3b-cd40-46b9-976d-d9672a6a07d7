version: '3.8'

services:
  # Zetpad API Service
  zetpad-api:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - AWS_REGION=ap-south-2
      - TABLE_PREFIX=zetpad_dev_
      - JWT_SECRET=your-jwt-secret-here
      - SESSION_SECRET=your-session-secret-here
      - GOOGLE_CLIENT_ID=your-google-client-id
      - GOOGLE_CLIENT_SECRET=your-google-client-secret
      - FRONTEND_URL=http://localhost:8080
      - CORS_ORIGIN=http://localhost:8080
      # For local development with LocalStack (keep as 'test')
      - A<PERSON>_ENDPOINT_URL=http://localstack:4566
      - AWS_ACCESS_KEY_ID=********************
      - AWS_SECRET_ACCESS_KEY=hTxPNph+PgqNZcsIf+Opc06/lG2n/VVgVeZo9Sne
      # For AWS deployment, replace with your real credentials:
      # - A<PERSON>_ACCESS_KEY_ID=your-real-access-key
      # - AWS_SECRET_ACCESS_KEY=your-real-secret-key
    depends_on:
      - localstack
    volumes:
      - ./src:/app/src:ro
      - ./build:/app/build
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # LocalStack for local AWS services
  localstack:
    image: localstack/localstack:latest
    ports:
      - "4566:4566"
    environment:
      - SERVICES=dynamodb
      - DEBUG=1
      - DATA_DIR=/tmp/localstack/data
      - DOCKER_HOST=unix:///var/run/docker.sock
    volumes:
      - "/tmp/localstack:/tmp/localstack"
      - "/var/run/docker.sock:/var/run/docker.sock"
    restart: unless-stopped

  # DynamoDB Admin UI (optional)
  dynamodb-admin:
    image: aaronshaf/dynamodb-admin:latest
    ports:
      - "8001:8001"
    environment:
      - DYNAMO_ENDPOINT=http://localstack:4566
    depends_on:
      - localstack
    restart: unless-stopped

networks:
  default:
    name: zetpad-network
