import { BaseModel, TableConfig, ValidationRules } from './base';
// import is from '../utils/validation';
import { ProjectCategory } from '../constants/projects';

export interface Project extends BaseModel {
  name: string;
  url?: string;
  description?: string;
  category: ProjectCategory;
  createdBy: string; // User ID
  folderId?: string; // Folder ID
}

export const ProjectValidations: ValidationRules = {
  // TODO: Fix validation types compatibility
  // name: [is.required(), is.maxLength(100)],
  // url: is.url(),
  // category: [is.required(), is.oneOf(Object.values(ProjectCategory))],
  // createdBy: is.required(),
};

export const ProjectTableConfig: TableConfig = {
  tableName: 'Projects',
  partitionKey: 'id',
  globalSecondaryIndexes: [
    {
      indexName: 'CreatedByIndex',
      partitionKey: 'createdBy',
      sortKey: 'createdAt',
      projectionType: 'ALL',
    },
    {
      indexName: 'FolderIndex',
      partitionKey: 'folderId',
      sortKey: 'name',
      projectionType: 'ALL',
    },
    {
      indexName: 'CategoryIndex',
      partitionKey: 'category',
      sortKey: 'createdAt',
      projectionType: 'ALL',
    },
  ],
};

// Helper function to create a new Project
export const createProject = (
  projectData: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>,
): Project => {
  const now = new Date().toISOString();
  return {
    id: '', // Will be set by the database layer
    ...projectData,
    createdAt: now,
    updatedAt: now,
  };
};

// Helper function to update a Project
export const updateProject = (
  existingProject: Project,
  updates: Partial<Omit<Project, 'id' | 'createdAt'>>,
): Project => {
  return {
    ...existingProject,
    ...updates,
    updatedAt: new Date().toISOString(),
  };
};
