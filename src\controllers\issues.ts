import { Issue, Comment, User } from '../models';
import { catchErrors } from 'errors';
import {
  updateEntity,
  deleteEntity,
  createEntity,
  findEntityOrThrow,
  queryEntitiesByIndex,
  // scanEntities, // Unused for now
} from 'utils/dynamodb';

export const getProjectIssues = catchErrors(async (req, res) => {
  const { searchTerm } = req.query;
  const projectId = '1'; // Convert to string for DynamoDB

  // Get issues by project using GSI
  let issues = await queryEntitiesByIndex<Issue>('Issue', 'ProjectIndex', projectId);

  // Filter by search term if provided
  if (searchTerm) {
    const searchTermLower = searchTerm.toString().toLowerCase();
    issues = issues.filter(
      issue =>
        issue.title.toLowerCase().includes(searchTermLower) ||
        (issue.descriptionText && issue.descriptionText.toLowerCase().includes(searchTermLower)),
    );
  }

  res.respond({ issues });
});

export const getIssueWithUsersAndComments = catchErrors(async (req, res) => {
  const issue = await findEntityOrThrow<Issue>('Issue', req.params.issueId);

  // Get comments for this issue
  const comments = await queryEntitiesByIndex<Comment>('Comment', 'IssueIndex', req.params.issueId);

  // Get users assigned to this issue
  const users: User[] = [];
  if (issue.userIds && issue.userIds.length > 0) {
    for (const userId of issue.userIds) {
      try {
        const user = await findEntityOrThrow<User>('User', userId);
        users.push(user);
      } catch (error) {
        // Skip if user not found
      }
    }
  }

  // Get user details for comments
  const commentsWithUsers = await Promise.all(
    comments.map(async comment => {
      try {
        const user = await findEntityOrThrow<User>('User', comment.userId);
        return { ...comment, user };
      } catch (error) {
        return comment;
      }
    }),
  );

  res.respond({
    issue: {
      ...issue,
      users,
      comments: commentsWithUsers,
    },
  });
});

export const create = catchErrors(async (req, res) => {
  const listPosition = await calculateListPosition(req.body);
  const issue = await createEntity<Issue>('Issue', { ...req.body, listPosition });
  res.respond({ issue });
});

export const update = catchErrors(async (req, res) => {
  const issue = await updateEntity<Issue>('Issue', req.params.issueId, req.body);
  res.respond({ issue });
});

export const remove = catchErrors(async (req, res) => {
  await deleteEntity('Issue', req.params.issueId);
  res.respond({ success: true });
});

const calculateListPosition = async ({ projectId, status }: Partial<Issue>): Promise<number> => {
  // Get all issues for this project with the same status
  const allProjectIssues = await queryEntitiesByIndex<Issue>('Issue', 'ProjectIndex', projectId!);
  const issues = allProjectIssues.filter(issue => issue.status === status);

  const listPositions = issues.map(({ listPosition }) => listPosition);

  if (listPositions.length > 0) {
    return Math.min(...listPositions) - 1;
  }
  return 1;
};
