// Base interfaces and types
export * from './base';

// Model interfaces and utilities
export * from './User';
export * from './Project';
export * from './Issue';
export * from './Comment';
export * from './Folder';
export * from './UserEntity';

// Table configurations for easy access
import { UserTableConfig } from './User';
import { ProjectTableConfig } from './Project';
import { IssueTableConfig } from './Issue';
import { CommentTableConfig } from './Comment';
import { FolderTableConfig } from './Folder';
import { UserEntityTableConfig } from './UserEntity';

export const TableConfigs = {
  User: UserTableConfig,
  Project: ProjectTableConfig,
  Issue: IssueTableConfig,
  Comment: CommentTableConfig,
  Folder: FolderTableConfig,
  UserEntity: UserEntityTableConfig,
};

// Model validations for easy access
import { UserValidations } from './User';
import { ProjectValidations } from './Project';
import { IssueValidations } from './Issue';
import { CommentValidations } from './Comment';
import { FolderValidations } from './Folder';
import { UserEntityValidations } from './UserEntity';

export const ModelValidations = {
  User: UserValidations,
  Project: ProjectValidations,
  Issue: IssueValidations,
  Comment: CommentValidations,
  Folder: FolderValidations,
  UserEntity: UserEntityValidations,
};
