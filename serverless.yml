service: zetpad-api

frameworkVersion: '3'

provider:
  name: aws
  runtime: nodejs18.x
  stage: ${opt:stage, 'dev'}
  region: ${opt:region, 'ap-south-2'}
  memorySize: 512
  timeout: 30
  
  environment:
    NODE_ENV: ${self:provider.stage}
    AWS_REGION: ${self:provider.region}
    TABLE_PREFIX: zetpad_${self:provider.stage}_
    JWT_SECRET: ${env:JWT_SECRET}
    SESSION_SECRET: ${env:SESSION_SECRET}
    GOOGLE_CLIENT_ID: ${env:GOOGLE_CLIENT_ID}
    GOOGLE_CLIENT_SECRET: ${env:GOOGLE_CLIENT_SECRET}
    FRONTEND_URL: ${env:FRONTEND_URL, 'http://localhost:8080'}
    CORS_ORIGIN: ${env:CORS_ORIGIN, '*'}

  iam:
    role:
      statements:
        - Effect: Allow
          Action:
            - dynamodb:Query
            - dynamodb:Scan
            - dynamodb:GetItem
            - dynamodb:PutItem
            - dynamodb:UpdateItem
            - dynamodb:DeleteItem
            - dynamodb:CreateTable
            - dynamodb:DescribeTable
            - dynamodb:ListTables
          Resource:
            - "arn:aws:dynamodb:${self:provider.region}:*:table/zetpad_${self:provider.stage}_*"
            - "arn:aws:dynamodb:${self:provider.region}:*:table/zetpad_${self:provider.stage}_*/index/*"

functions:
  api:
    handler: build/lambda.handler
    events:
      - http:
          path: /{proxy+}
          method: ANY
          cors:
            origin: ${env:CORS_ORIGIN, '*'}
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
            allowCredentials: true
      - http:
          path: /
          method: ANY
          cors:
            origin: ${env:CORS_ORIGIN, '*'}
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
            allowCredentials: true

plugins:
  - serverless-offline

custom:
  serverless-offline:
    httpPort: 3000
    host: 0.0.0.0

package:
  exclude:
    - node_modules/**
    - .git/**
    - .env
    - README.md
    - .gitignore
    - .eslintrc.js
    - .prettierrc
    - nodemon.json
    - src/**
    - "!build/**"
  include:
    - build/**
    - node_modules/**
