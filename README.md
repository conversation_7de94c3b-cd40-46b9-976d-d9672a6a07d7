# Zetpad API - AWS Lambda Deployment Guide 🚀

A serverless Node.js API built with Express.js, deployed on AWS Lambda using Docker containers, with DynamoDB as the database.

## 🌐 Live API Endpoint

**Production API**: `https://y15r443i3k.execute-api.ap-south-2.amazonaws.com/prod`

## 📋 Table of Contents

- [Project Structure](#project-structure)
- [Architecture Overview](#architecture-overview)
- [Prerequisites](#prerequisites)
- [Local Development Setup](#local-development-setup)
- [AWS Lambda Deployment](#aws-lambda-deployment)
- [Updating Production API](#updating-production-api)
- [Environment Variables](#environment-variables)
- [API Endpoints](#api-endpoints)
- [Troubleshooting](#troubleshooting)

## 🏗 Project Structure

| File or folder    | Description                                                                                                                                                                                                                 |
| ----------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `src/index.ts`    | The entry file. This is where we setup middleware, attach routes, initialize database and express.                                                                                                                          |
| `src/routes.ts`   | This is where we define all routes, both public and private.                                                                                                                                                                |
| `src/constants`   | Constants are values that never change and are used in multiple places across the codebase.                                                                                                                                 |
| `src/controllers` | Controllers listen to client's requests and work with entities and the database to fetch, add, update, or delete data.                                                                                                      |
| `src/database`    | Database related code and seeds go here.                                                                                                                                                                                    |
| `src/models`      | This is where we define DynamoDB models and table configurations. We define interfaces, validations, and table schemas for each data model.                                                                                 |
| `src/errors`      | This is where we define custom errors. The `catchErrors` function helps us avoid repetitive `try/catch` blocks within controllers.                                                                                          |
| `src/middleware`  | Middleware functions can modify request and response objects, end the request-response cycle, etc. For example `authenticateUser` method verifies the authorization token and attaches `currentUser` to the request object. |
| `src/serializers` | Serializers transform the data fetched from the database before it's sent to the client.                                                                                                                                    |
| `src/utils`       | Utility(helper) functions that are used in multiple places across the codebase. For example `utils/dynamodb.ts` functions help us validate data and avoid writing repetitive code.                                          |
| `build/`          | Compiled JavaScript output from TypeScript source code                                                                                                                                                                      |
| `Dockerfile`      | Docker configuration for containerized Lambda deployment                                                                                                                                                                    |

## 🏛 Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐    ┌──────────────────┐
│   Client App    │───▶│   API Gateway    │───▶│  AWS Lambda     │───▶│    DynamoDB      │
│  (Frontend)     │    │  (REST API)      │    │ (Docker Image)  │    │   (Database)     │
└─────────────────┘    └──────────────────┘    └─────────────────┘    └──────────────────┘
                                │                        │
                                │                        ▼
                                │               ┌─────────────────┐
                                │               │  CloudWatch     │
                                │               │    (Logs)       │
                                │               └─────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │       ECR        │
                       │ (Container Repo) │
                       └──────────────────┘
```

## ✅ Prerequisites

Before deploying, ensure you have:

1. **AWS CLI** installed and configured
2. **Docker** installed and running
3. **Node.js** (v18 or higher)
4. **npm** or **yarn**
5. **AWS Account** with appropriate permissions

### AWS CLI Configuration

```bash
# Configure AWS credentials
aws configure
# Enter your AWS Access Key ID, Secret Access Key, Region (ap-south-2), and output format (json)

# Verify configuration
aws sts get-caller-identity
```

## 🛠 Local Development Setup

### 1. Install Dependencies

```bash
npm install
```

### 2. Environment Variables

Create a `.env` file in the root directory:

```env
# Database Configuration
AWS_REGION=ap-south-2
AWS_ACCESS_KEY_ID=your-access-key-id
AWS_SECRET_ACCESS_KEY=your-secret-access-key
DYNAMODB_ENDPOINT=http://localhost:8000  # For local development
TABLE_PREFIX=zetpad_dev_

# Authentication
JWT_SECRET=your-jwt-secret-here
SESSION_SECRET=your-session-secret-here
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Application
NODE_ENV=development
CORS_ORIGIN=http://localhost:3000
FRONTEND_URL=http://localhost:3000
```

### 3. Local DynamoDB Setup

```bash
# Install DynamoDB Local
npm install -g dynamodb-local

# Run DynamoDB Local
dynamodb-local
```

### 4. Build and Run

```bash
# Build TypeScript
npm run build

# Start development server
npm run dev
```

## 🚀 AWS Lambda Deployment

### Step 1: Build the Application

```bash
# Install dependencies and build
npm install
npm run build
```

### Step 2: Create ECR Repository

```bash
# Create ECR repository
aws ecr create-repository --repository-name zetpad-api --region ap-south-2

# Get login token and authenticate Docker
aws ecr get-login-password --region ap-south-2 | docker login --username AWS --password-stdin 891377014356.dkr.ecr.ap-south-2.amazonaws.com
```

### Step 3: Build and Push Docker Image

```bash
# Build Docker image
docker build -t zetpad-api .

# Tag image for ECR
docker tag zetpad-api:latest 891377014356.dkr.ecr.ap-south-2.amazonaws.com/zetpad-api:latest

# Push to ECR
docker push 891377014356.dkr.ecr.ap-south-2.amazonaws.com/zetpad-api:latest
```

### Step 4: Create IAM Role for Lambda

```bash
# Create trust policy file
cat > trust-policy.json << EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
EOF

# Create IAM role
aws iam create-role --role-name zetpad-lambda-execution-role --assume-role-policy-document file://trust-policy.json

# Attach basic Lambda execution policy
aws iam attach-role-policy --role-name zetpad-lambda-execution-role --policy-arn arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole

# Create DynamoDB policy
cat > dynamodb-policy.json << EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "dynamodb:GetItem",
        "dynamodb:PutItem",
        "dynamodb:UpdateItem",
        "dynamodb:DeleteItem",
        "dynamodb:Query",
        "dynamodb:Scan",
        "dynamodb:CreateTable",
        "dynamodb:DescribeTable"
      ],
      "Resource": "*"
    }
  ]
}
EOF

# Create and attach DynamoDB policy
aws iam create-policy --policy-name zetpad-dynamodb-policy --policy-document file://dynamodb-policy.json
aws iam attach-role-policy --role-name zetpad-lambda-execution-role --policy-arn arn:aws:iam::891377014356:policy/zetpad-dynamodb-policy
```

### Step 5: Create Lambda Function

```bash
# Create Lambda function with container image
aws lambda create-function \
  --function-name zetpad-api \
  --package-type Image \
  --code ImageUri=891377014356.dkr.ecr.ap-south-2.amazonaws.com/zetpad-api:latest \
  --role arn:aws:iam::891377014356:role/zetpad-lambda-execution-role \
  --timeout 30 \
  --memory-size 512 \
  --environment Variables='{
    "NODE_ENV":"production",
    "AWS_REGION":"ap-south-2",
    "TABLE_PREFIX":"zetpad_prod_",
    "JWT_SECRET":"your-jwt-secret-here",
    "SESSION_SECRET":"your-session-secret-here",
    "GOOGLE_CLIENT_ID":"your-google-client-id",
    "GOOGLE_CLIENT_SECRET":"your-google-client-secret",
    "CORS_ORIGIN":"https://your-frontend-domain.com",
    "FRONTEND_URL":"https://your-frontend-domain.com"
  }'
```

### Step 6: Create API Gateway

```bash
# Create REST API
aws apigateway create-rest-api --name zetpad-api --region ap-south-2

# Get the API ID (replace with your actual API ID)
API_ID="y15r443i3k"

# Get root resource ID
ROOT_RESOURCE_ID=$(aws apigateway get-resources --rest-api-id $API_ID --query 'items[?path==`/`].id' --output text)

# Create proxy resource
aws apigateway create-resource \
  --rest-api-id $API_ID \
  --parent-id $ROOT_RESOURCE_ID \
  --path-part "{proxy+}"

# Get proxy resource ID
PROXY_RESOURCE_ID=$(aws apigateway get-resources --rest-api-id $API_ID --query 'items[?pathPart==`{proxy+}`].id' --output text)

# Create ANY method for proxy resource
aws apigateway put-method \
  --rest-api-id $API_ID \
  --resource-id $PROXY_RESOURCE_ID \
  --http-method ANY \
  --authorization-type NONE

# Set up Lambda integration
aws apigateway put-integration \
  --rest-api-id $API_ID \
  --resource-id $PROXY_RESOURCE_ID \
  --http-method ANY \
  --type AWS_PROXY \
  --integration-http-method POST \
  --uri arn:aws:apigateway:ap-south-2:lambda:path/2015-03-31/functions/arn:aws:lambda:ap-south-2:891377014356:function:zetpad-api/invocations

# Grant API Gateway permission to invoke Lambda
aws lambda add-permission \
  --function-name zetpad-api \
  --statement-id apigateway-invoke \
  --action lambda:InvokeFunction \
  --principal apigateway.amazonaws.com \
  --source-arn "arn:aws:execute-api:ap-south-2:891377014356:$API_ID/*/*"

# Deploy API
aws apigateway create-deployment \
  --rest-api-id $API_ID \
  --stage-name prod
```

### Step 7: Test Deployment

```bash
# Test health endpoint
curl -X GET "https://y15r443i3k.execute-api.ap-south-2.amazonaws.com/prod/health"

# Expected response:
# {"status":"OK","timestamp":"2025-07-03T14:55:41.576Z"}
```

## 🔄 Updating Production API

### Quick Update Process

After making code changes, follow these steps to update the production API:

#### 1. Build and Test Locally

```bash
# Build the application
npm run build

# Test locally (optional but recommended)
npm run dev
```

#### 2. Build and Push Updated Docker Image

```bash
# Authenticate Docker with ECR
aws ecr get-login-password --region ap-south-2 | docker login --username AWS --password-stdin 891377014356.dkr.ecr.ap-south-2.amazonaws.com

# Build new Docker image
docker build -t zetpad-api .

# Tag the image
docker tag zetpad-api:latest 891377014356.dkr.ecr.ap-south-2.amazonaws.com/zetpad-api:latest

# Push to ECR
docker push 891377014356.dkr.ecr.ap-south-2.amazonaws.com/zetpad-api:latest
```

#### 3. Update Lambda Function

```bash
# Update Lambda function with new image
aws lambda update-function-code \
  --function-name zetpad-api \
  --image-uri 891377014356.dkr.ecr.ap-south-2.amazonaws.com/zetpad-api:latest

# Update Lambda function with new image 2
aws lambda update-function-code --function-name zetpad-api --image-uri 891377014356.dkr.ecr.ap-south-2.amazonaws.com/zetpad-api:latest

# Wait for update to complete (optional)
aws lambda wait function-updated --function-name zetpad-api

# You can verify the update is complete by checking the function status:
aws lambda get-function --function-name zetpad-api
```

#### 4. Test Updated API

```bash
# Test health endpoint
curl -X GET "https://y15r443i3k.execute-api.ap-south-2.amazonaws.com/prod/health"

# Test authenticated endpoint (should return 401 without token)
curl -X GET "https://y15r443i3k.execute-api.ap-south-2.amazonaws.com/prod/api/notes"
```

### Automated Update Script

Create a `deploy.sh` script for easier updates:

```bash
#!/bin/bash
set -e

echo "🚀 Starting deployment..."

# Build application
echo "📦 Building application..."
npm run build

# Docker operations
echo "🐳 Building Docker image..."
docker build -t zetpad-api .

echo "🏷️ Tagging image..."
docker tag zetpad-api:latest 891377014356.dkr.ecr.ap-south-2.amazonaws.com/zetpad-api:latest

echo "🔐 Authenticating with ECR..."
aws ecr get-login-password --region ap-south-2 | docker login --username AWS --password-stdin 891377014356.dkr.ecr.ap-south-2.amazonaws.com

echo "⬆️ Pushing to ECR..."
docker push 891377014356.dkr.ecr.ap-south-2.amazonaws.com/zetpad-api:latest

echo "🔄 Updating Lambda function..."
aws lambda update-function-code \
  --function-name zetpad-api \
  --image-uri 891377014356.dkr.ecr.ap-south-2.amazonaws.com/zetpad-api:latest

echo "⏳ Waiting for update to complete..."
aws lambda wait function-updated --function-name zetpad-api

echo "✅ Testing deployment..."
curl -X GET "https://y15r443i3k.execute-api.ap-south-2.amazonaws.com/prod/health"

echo "🎉 Deployment completed successfully!"
```

Make it executable:

```bash
chmod +x deploy.sh
./deploy.sh
```

## 🔧 Environment Variables

### Production Environment Variables

Set these in your Lambda function:

| Variable               | Description                | Example                            |
| ---------------------- | -------------------------- | ---------------------------------- |
| `NODE_ENV`             | Environment mode           | `production`                       |
| `AWS_REGION`           | AWS region                 | `ap-south-2`                       |
| `TABLE_PREFIX`         | DynamoDB table prefix      | `zetpad_prod_`                     |
| `JWT_SECRET`           | JWT signing secret         | `your-secure-jwt-secret`           |
| `SESSION_SECRET`       | Session secret             | `your-secure-session-secret`       |
| `GOOGLE_CLIENT_ID`     | Google OAuth client ID     | `your-google-client-id`            |
| `GOOGLE_CLIENT_SECRET` | Google OAuth client secret | `your-google-client-secret`        |
| `CORS_ORIGIN`          | Allowed CORS origin        | `https://your-frontend-domain.com` |
| `FRONTEND_URL`         | Frontend URL for redirects | `https://your-frontend-domain.com` |

### Update Environment Variables

```bash
# Update Lambda environment variables
aws lambda update-function-configuration \
  --function-name zetpad-api \
  --environment Variables='{
    "NODE_ENV":"production",
    "AWS_REGION":"ap-south-2",
    "TABLE_PREFIX":"zetpad_prod_",
    "JWT_SECRET":"your-new-jwt-secret",
    "SESSION_SECRET":"your-new-session-secret",
    "GOOGLE_CLIENT_ID":"your-google-client-id",
    "GOOGLE_CLIENT_SECRET":"your-google-client-secret",
    "CORS_ORIGIN":"https://your-frontend-domain.com",
    "FRONTEND_URL":"https://your-frontend-domain.com"
  }'
```

## 📡 API Endpoints

### Public Endpoints

| Method | Endpoint           | Description                 |
| ------ | ------------------ | --------------------------- |
| `GET`  | `/health`          | Health check endpoint       |
| `POST` | `/api/auth/google` | Google OAuth authentication |

### Protected Endpoints (Require Authentication)

| Method   | Endpoint         | Description       |
| -------- | ---------------- | ----------------- |
| `GET`    | `/api/notes`     | Get user's notes  |
| `POST`   | `/api/notes`     | Create a new note |
| `PUT`    | `/api/notes/:id` | Update a note     |
| `DELETE` | `/api/notes/:id` | Delete a note     |

### Example API Usage

```bash
# Health check
curl -X GET "https://y15r443i3k.execute-api.ap-south-2.amazonaws.com/prod/health"

# Google OAuth (redirect to Google)
curl -X POST "https://y15r443i3k.execute-api.ap-south-2.amazonaws.com/prod/api/auth/google"

# Get notes (requires JWT token)
curl -X GET "https://y15r443i3k.execute-api.ap-south-2.amazonaws.com/prod/api/notes" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Create note (requires JWT token)
curl -X POST "https://y15r443i3k.execute-api.ap-south-2.amazonaws.com/prod/api/notes" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"title":"My Note","content":"Note content"}'
```

## 🐳 Docker Configuration

### Dockerfile Explanation

```dockerfile
# Use AWS Lambda Node.js 18 base image
FROM public.ecr.aws/lambda/nodejs:18

# Copy package files
COPY package*.json ./

# Install production dependencies only
RUN npm install --omit=dev

# Copy built application
COPY build/ ./

# Set Lambda handler
CMD ["lambda.handler"]
```

### Key Points:

- **Base Image**: Uses official AWS Lambda Node.js 18 runtime
- **Production Dependencies**: Only installs production dependencies to reduce image size
- **Build Directory**: Copies pre-built JavaScript from `build/` directory
- **Handler**: Points to `lambda.handler` function in the built code
- **No Module Alias**: Removed `module-alias` for Lambda compatibility, using relative imports

### Build Optimization

The Docker build is optimized for:

- **Fast builds**: ~11.9 seconds build time
- **Small context**: ~1MB build context (excludes node_modules, src, etc.)
- **Layer caching**: Efficient layer caching for dependencies

## 🔍 Troubleshooting

### Common Issues and Solutions

#### 1. Module Resolution Errors

**Problem**: `TypeError: Cannot read properties of undefined (reading '_simulateRepl')`

**Solution**: This was caused by `module-alias` package incompatibility with Lambda runtime. Fixed by:

- Removing `module-alias` dependency
- Converting all module alias imports to relative imports
- Updating build configuration

#### 2. Docker Build Context Too Large

**Problem**: Docker build taking too long or failing due to large context

**Solution**: Add `.dockerignore` file:

```
node_modules
src
.git
.env
*.md
.vscode
.gitignore
```

#### 3. ECR Authentication Issues

**Problem**: `no basic auth credentials` error when pushing to ECR

**Solution**:

```bash
# Re-authenticate with ECR
aws ecr get-login-password --region ap-south-2 | docker login --username AWS --password-stdin 891377014356.dkr.ecr.ap-south-2.amazonaws.com
```

#### 4. Lambda Function Not Updating

**Problem**: Lambda function not reflecting new code changes

**Solution**:

```bash
# Force update with new image digest
aws lambda update-function-code \
  --function-name zetpad-api \
  --image-uri 891377014356.dkr.ecr.ap-south-2.amazonaws.com/zetpad-api:latest

# Wait for update to complete
aws lambda wait function-updated --function-name zetpad-api
```

#### 5. DynamoDB Connection Issues

**Problem**: DynamoDB connection errors in Lambda

**Solution**: Ensure:

- IAM role has DynamoDB permissions
- Environment variables are set correctly
- AWS region is consistent across all services

#### 6. API Gateway 502 Errors

**Problem**: API Gateway returning 502 Bad Gateway

**Solution**: Check:

- Lambda function logs in CloudWatch
- Lambda function timeout settings (increase if needed)
- Memory allocation (increase if needed)

### Viewing Logs

```bash
# Get latest log stream
LOG_STREAM=$(aws logs describe-log-streams \
  --log-group-name /aws/lambda/zetpad-api \
  --order-by LastEventTime \
  --descending \
  --max-items 1 \
  --query 'logStreams[0].logStreamName' \
  --output text)

# View recent logs
aws logs get-log-events \
  --log-group-name /aws/lambda/zetpad-api \
  --log-stream-name $LOG_STREAM \
  --limit 50
```

### Performance Monitoring

Monitor your Lambda function performance:

```bash
# Get function configuration
aws lambda get-function-configuration --function-name zetpad-api

# View CloudWatch metrics
aws cloudwatch get-metric-statistics \
  --namespace AWS/Lambda \
  --metric-name Duration \
  --dimensions Name=FunctionName,Value=zetpad-api \
  --start-time 2025-07-03T00:00:00Z \
  --end-time 2025-07-03T23:59:59Z \
  --period 3600 \
  --statistics Average,Maximum
```

## 📚 Additional Resources

- [AWS Lambda Container Images](https://docs.aws.amazon.com/lambda/latest/dg/images-create.html)
- [Amazon ECR User Guide](https://docs.aws.amazon.com/AmazonECR/latest/userguide/)
- [API Gateway Lambda Integration](https://docs.aws.amazon.com/apigateway/latest/developerguide/set-up-lambda-integrations.html)
- [DynamoDB Developer Guide](https://docs.aws.amazon.com/amazondynamodb/latest/developerguide/)

---

**🎉 Your Zetpad API is now successfully deployed on AWS Lambda with Docker containers!**

For any issues or questions, check the troubleshooting section above or review the CloudWatch logs.
